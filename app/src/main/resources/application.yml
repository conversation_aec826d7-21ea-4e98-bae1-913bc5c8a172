##########################################################
#############       Global properties        #############
##########################################################

# We always listen on the same port, regardless of "env"
server:
  port: 44760

management:
  influx:
    metrics:
      export:
        enabled: true

graphite:
  enable: false

spring:
  mvc:
    async:
      request-timeout: 1800s

# Disable JMS functionality completely
tisp:
  jms:
    enabled: false
    local:
      disable-retries: true

servicediscovery:
  auth: http://auth:13880
  semss: http://sems-security-server:44110
  sem1core: http://sem1core:44630
  sus: http://sems-usage-server:47380
  subr: http://subscriptionrepository-server:11580/subscription-repository

---
#Start of environment specific properties

##########################################################
######      CI/CD (Delivery Engine) specific        ######
##########################################################

spring.config.activate.on-profile: de_component-test_eu-west-1

servicediscovery:
  auth: http://mockhost:8080/
  semss: http://mockhost:8080/
  semscore: http://mockhost:8080/
  subr: http://mockhost:8080/

fileshare:
  url1: https://vt.iot1.sems.ws:443/fileshare/v1/fileshare
  path: /var/opt/vgt/sems1-vehicle-api-server-portal/software

invalidtoken:
  url: http://localhost:44760/security/v1/api/login

natoken:
  url: http://localhost:44760/api/v1/logon

cloudfront:
  url: https://download1-cf.iot1.sems.ws

bucket:
  name: bucket.semspo.iot1.shared.eu-west-1.qa.aws.vgthosting.net

aws:
  region: eu-west-1

accessKeyId: ********************
secretAccessKey: tKWdbzJG6uvsxpNd7TbKk7mnVUVe/AAIQfGgExAp

management:
  influx:
    metrics:
      export:
        enabled: false

spring:
  artemis:
    broker-url: tcp://mockhost:61616

---
##########################################################
#########             LOCAL SETTINGS             #########
##########################################################

spring.config.activate.on-profile: local_local_local

management:
  influx:
    metrics:
      export:
        enabled: false

graphite:
  server: graphite-dev.vgt.volvo.com
  enable: false

servicediscovery:
  auth: http://localhost:8080/
  semss: http://localhost:44110/
  sem1core: http://localhost:44630
  sus: http://localhost:47380/

fileshare:
  url1: https://vt.iot1.sems.ws:443/fileshare/v1/fileshare
  path: /var/opt/vgt/sems1-vehicle-api-server-portal/software

invalidtoken:
  url: http://localhost:44760/security/v1/api/login

natoken:
  url: http://localhost:44760/api/v1/logon

cloudfront:
  url: https://download1-cf.qa.sems.ws

bucket:
  name: bucket.semspo.qa.shared.eu-west-1.qa.aws.vgthosting.net

aws:
  region: eu-west-1

presigndownload:
  enable: true