package com.volvo.tisp.s1vas.conf;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
@ConditionalOnProperty(name = "tisp.jms.enabled", havingValue = "true", matchIfMissing = false)
public class JmsConfig {

  @Autowired private JmsProperties jmsProperties;

  @Bean
  @Primary
  public JmsSystemPropertiesConfigurer jmsSystemPropertiesConfigurer() {
    return new JmsSystemPropertiesConfigurer(jmsProperties);
  }

  public static class JmsSystemPropertiesConfigurer {

    private static final Logger LOG = LoggerFactory.getLogger(JmsSystemPropertiesConfigurer.class);

    public JmsSystemPropertiesConfigurer(JmsProperties jmsProperties) {
      configureArtemisProperties(jmsProperties);
    }

    private void configureArtemisProperties(JmsProperties jmsProperties) {
      System.setProperty(
          "artemis.jms.consumer.receive.timeout",
          String.valueOf(jmsProperties.getConsumer().getTimeout()));

      System.setProperty(
          "artemis.jms.connection.ttl", String.valueOf(jmsProperties.getConnection().getTtl()));

      System.setProperty(
          "artemis.jms.call.timeout",
          String.valueOf(jmsProperties.getConnection().getCallTimeout()));

      System.setProperty(
          "artemis.jms.consumer.window.size",
          String.valueOf(jmsProperties.getConsumer().getWindowSize()));

      System.setProperty(
          "artemis.jms.connection.pool.size",
          String.valueOf(jmsProperties.getConnection().getPoolSize()));

      LOG.info("JMS Configuration Applied:");
      LOG.info("  Consumer Timeout: {}ms", jmsProperties.getConsumer().getTimeout());
      LOG.info("  Connection TTL: {}ms", jmsProperties.getConnection().getTtl());
      LOG.info("  Call Timeout: {}ms", jmsProperties.getConnection().getCallTimeout());
      LOG.info("  Consumer Window Size: {} bytes", jmsProperties.getConsumer().getWindowSize());
      LOG.info("  Connection Pool Size: {}", jmsProperties.getConnection().getPoolSize());
    }
  }
}
