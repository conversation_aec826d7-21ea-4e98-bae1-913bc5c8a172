package com.volvo.tisp.s1vas.conf;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;

/**
 * Local development JMS configuration to prevent connection retry errors
 * when JMS broker is not available in local environment.
 */
@Configuration
@Profile("local_local_local")
public class LocalJmsConfig {

    private static final Logger LOG = LoggerFactory.getLogger(LocalJmsConfig.class);

    /**
     * Override JMS listener container factory for local development
     * to disable auto-startup and prevent connection retry errors
     */
    @Bean
    @ConditionalOnProperty(name = "tisp.jms.local.disable-retries", havingValue = "true", matchIfMissing = false)
    public DefaultJmsListenerContainerFactory localJmsListenerContainerFactory() {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        
        // Disable auto startup for local development
        factory.setAutoStartup(false);
        
        LOG.info("Local JMS configuration applied - JMS listeners disabled for local development");
        
        return factory;
    }
}
