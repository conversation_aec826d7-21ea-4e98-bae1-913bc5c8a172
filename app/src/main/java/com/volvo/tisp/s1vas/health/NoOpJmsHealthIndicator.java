package com.volvo.tisp.s1vas.health;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * No-op health indicator for when <PERSON><PERSON> is disabled.
 * This provides a health status without attempting any JMS connections.
 */
@Component
@ConditionalOnProperty(name = "tisp.jms.enabled", havingValue = "false", matchIfMissing = false)
public class NoOpJmsHealthIndicator implements HealthIndicator {

    private static final Logger LOG = LoggerFactory.getLogger(NoOpJmsHealthIndicator.class);

    @Override
    public Health health() {
        LOG.debug("JMS Health Check - JMS is disabled");
        return Health.up()
            .withDetail("status", "JMS is disabled")
            .withDetail("message", "JMS functionality has been disabled in configuration")
            .withDetail("tisp.jms.enabled", "false")
            .build();
    }
}
