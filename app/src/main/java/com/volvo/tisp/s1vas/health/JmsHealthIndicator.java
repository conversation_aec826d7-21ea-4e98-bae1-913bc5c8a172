package com.volvo.tisp.s1vas.health;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Component;

import jakarta.jms.Connection;
import jakarta.jms.ConnectionFactory;
import jakarta.jms.JMSException;

/**
 * Health indicator for JMS connections to monitor and prevent thread blocking issues.
 * This helps detect when JMS connections are in problematic states.
 */
@Component
public class JmsHealthIndicator implements HealthIndicator {

    private static final Logger LOG = LoggerFactory.getLogger(JmsHealthIndicator.class);

    @Autowired
    private ConnectionFactory connectionFactory;

    @Autowired
    private JmsTemplate jmsTemplate;

    @Override
    public Health health() {
        try {
            // Test connection creation and closure
            Connection connection = connectionFactory.createConnection();
            
            if (connection != null) {
                // Test connection start/stop to ensure it's responsive
                connection.start();
                
                // Get connection metadata for health details
                String clientId = connection.getClientID();
                
                connection.stop();
                connection.close();
                
                return Health.up()
                    .withDetail("connectionFactory", connectionFactory.getClass().getSimpleName())
                    .withDetail("clientId", clientId != null ? clientId : "default")
                    .withDetail("receiveTimeout", jmsTemplate.getReceiveTimeout())
                    .withDetail("status", "Connection test successful")
                    .build();
            } else {
                return Health.down()
                    .withDetail("error", "Unable to create JMS connection")
                    .build();
            }
            
        } catch (JMSException e) {
            LOG.error("JMS Health Check failed", e);
            return Health.down()
                .withDetail("error", e.getMessage())
                .withDetail("errorCode", e.getErrorCode())
                .withDetail("linkedException", e.getLinkedException() != null ? 
                    e.getLinkedException().getMessage() : "None")
                .build();
        } catch (Exception e) {
            LOG.error("JMS Health Check failed with unexpected error", e);
            return Health.down()
                .withDetail("error", e.getMessage())
                .withDetail("type", e.getClass().getSimpleName())
                .build();
        }
    }
}
