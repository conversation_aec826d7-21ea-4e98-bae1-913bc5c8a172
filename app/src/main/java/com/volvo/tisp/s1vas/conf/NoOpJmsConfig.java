/*
package com.volvo.tisp.s1vas.conf;

import jakarta.jms.*;
import java.io.Serializable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.listener.DefaultMessageListenerContainer;

@Configuration
@ConditionalOnProperty(name = "tisp.jms.enabled", havingValue = "false", matchIfMissing = false)
public class NoOpJmsConfig {

  private static final Logger LOG = LoggerFactory.getLogger(NoOpJmsConfig.class);

  @Bean
  @Primary
  public ConnectionFactory noOpConnectionFactory() {
    LOG.info("Creating no-op ConnectionFactory - JMS is disabled");
    return new NoOpConnectionFactory();
  }

  @Bean
  @Primary
  public JmsTemplate noOpJmsTemplate() {
    LOG.info("Creating no-op JmsTemplate - JMS is disabled");
    JmsTemplate template = new JmsTemplate(noOpConnectionFactory());
    template.setReceiveTimeout(1000);
    return template;
  }

  @Bean
  @Primary
  public DefaultMessageListenerContainer noOpJmsListenerContainer() {
    LOG.info("Creating no-op JMS Listener Container - JMS is disabled");
    DefaultMessageListenerContainer container = new DefaultMessageListenerContainer();
    container.setConnectionFactory(noOpConnectionFactory());
    container.setDestinationName("no-op-destination");
    container.setAutoStartup(false);
    return container;
  }

  private static class NoOpConnectionFactory implements ConnectionFactory {
    @Override
    public Connection createConnection() throws JMSException {
      return new NoOpConnection();
    }

    @Override
    public Connection createConnection(String userName, String password) throws JMSException {
      return new NoOpConnection();
    }

    @Override
    public JMSContext createContext() {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public JMSContext createContext(String userName, String password) {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public JMSContext createContext(String userName, String password, int sessionMode) {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public JMSContext createContext(int sessionMode) {
      throw new UnsupportedOperationException("JMS is disabled");
    }
  }

  private static class NoOpConnection implements Connection {
    @Override
    public Session createSession(boolean transacted, int acknowledgeMode) throws JMSException {
      return new NoOpSession();
    }

    @Override
    public Session createSession(int sessionMode) throws JMSException {
      return new NoOpSession();
    }

    @Override
    public Session createSession() throws JMSException {
      return new NoOpSession();
    }

    @Override
    public String getClientID() throws JMSException {
      return "no-op-client";
    }

    @Override
    public void setClientID(String clientID) throws JMSException {}

    @Override
    public ConnectionMetaData getMetaData() throws JMSException {
      return null;
    }

    @Override
    public ExceptionListener getExceptionListener() throws JMSException {
      return null;
    }

    @Override
    public void setExceptionListener(ExceptionListener listener) throws JMSException {}

    @Override
    public void start() throws JMSException {}

    @Override
    public void stop() throws JMSException {}

    @Override
    public void close() throws JMSException {}

    @Override
    public ConnectionConsumer createConnectionConsumer(
        Destination destination,
        String messageSelector,
        ServerSessionPool sessionPool,
        int maxMessages)
        throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public ConnectionConsumer createSharedConnectionConsumer(
        Topic topic,
        String subscriptionName,
        String messageSelector,
        ServerSessionPool sessionPool,
        int maxMessages)
        throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public ConnectionConsumer createDurableConnectionConsumer(
        Topic topic,
        String subscriptionName,
        String messageSelector,
        ServerSessionPool sessionPool,
        int maxMessages)
        throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public ConnectionConsumer createSharedDurableConnectionConsumer(
        Topic topic,
        String subscriptionName,
        String messageSelector,
        ServerSessionPool sessionPool,
        int maxMessages)
        throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }
  }

  private static class NoOpSession implements Session {
    @Override
    public BytesMessage createBytesMessage() throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public MapMessage createMapMessage() throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public Message createMessage() throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public ObjectMessage createObjectMessage() throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public ObjectMessage createObjectMessage(Serializable object) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public StreamMessage createStreamMessage() throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public TextMessage createTextMessage() throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public TextMessage createTextMessage(String text) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public boolean getTransacted() throws JMSException {
      return false;
    }

    @Override
    public int getAcknowledgeMode() throws JMSException {
      return AUTO_ACKNOWLEDGE;
    }

    @Override
    public void commit() throws JMSException {}

    @Override
    public void rollback() throws JMSException {}

    @Override
    public void close() throws JMSException {}

    @Override
    public void recover() throws JMSException {}

    @Override
    public MessageListener getMessageListener() throws JMSException {
      return null;
    }

    @Override
    public void setMessageListener(MessageListener listener) throws JMSException {}

    @Override
    public void run() {}

    @Override
    public MessageProducer createProducer(Destination destination) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public MessageConsumer createConsumer(Destination destination) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public MessageConsumer createConsumer(Destination destination, String messageSelector)
        throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public MessageConsumer createConsumer(
        Destination destination, String messageSelector, boolean noLocal) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public MessageConsumer createSharedConsumer(Topic topic, String sharedSubscriptionName)
        throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public MessageConsumer createSharedConsumer(
        Topic topic, String sharedSubscriptionName, String messageSelector) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public Queue createQueue(String queueName) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public Topic createTopic(String topicName) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public TopicSubscriber createDurableSubscriber(Topic topic, String name) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public TopicSubscriber createDurableSubscriber(
        Topic topic, String name, String messageSelector, boolean noLocal) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public MessageConsumer createDurableConsumer(Topic topic, String name) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public MessageConsumer createDurableConsumer(
        Topic topic, String name, String messageSelector, boolean noLocal) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public MessageConsumer createSharedDurableConsumer(Topic topic, String name)
        throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public MessageConsumer createSharedDurableConsumer(
        Topic topic, String name, String messageSelector) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public QueueBrowser createBrowser(Queue queue) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public QueueBrowser createBrowser(Queue queue, String messageSelector) throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public TemporaryQueue createTemporaryQueue() throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public TemporaryTopic createTemporaryTopic() throws JMSException {
      throw new UnsupportedOperationException("JMS is disabled");
    }

    @Override
    public void unsubscribe(String name) throws JMSException {}
  }
}
*/
