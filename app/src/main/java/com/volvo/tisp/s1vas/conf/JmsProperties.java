package com.volvo.tisp.s1vas.conf;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "tisp.jms")
public class JmsProperties {

  private boolean enabled = true;
  private Consumer consumer = new Consumer();
  private Connection connection = new Connection();
  private Listener listener = new Listener();

  public boolean isEnabled() {
    return enabled;
  }

  public void setEnabled(boolean enabled) {
    this.enabled = enabled;
  }

  public Consumer getConsumer() {
    return consumer;
  }

  public void setConsumer(Consumer consumer) {
    this.consumer = consumer;
  }

  public Connection getConnection() {
    return connection;
  }

  public void setConnection(Connection connection) {
    this.connection = connection;
  }

  public Listener getListener() {
    return listener;
  }

  public void setListener(Listener listener) {
    this.listener = listener;
  }

  public static class Consumer {
    private long timeout = 10000;
    private long maxWait = 10000;
    private int windowSize = 1024 * 1024;

    public long getTimeout() {
      return timeout;
    }

    public void setTimeout(long timeout) {
      this.timeout = timeout;
    }

    public long getMaxWait() {
      return maxWait;
    }

    public void setMaxWait(long maxWait) {
      this.maxWait = maxWait;
    }

    public int getWindowSize() {
      return windowSize;
    }

    public void setWindowSize(int windowSize) {
      this.windowSize = windowSize;
    }
  }

  public static class Connection {
    private int poolSize = 10;
    private long timeout = 60000;
    private long retryInterval = 5000;
    private long ttl = 60000;
    private long callTimeout = 30000;

    public int getPoolSize() {
      return poolSize;
    }

    public void setPoolSize(int poolSize) {
      this.poolSize = poolSize;
    }

    public long getTimeout() {
      return timeout;
    }

    public void setTimeout(long timeout) {
      this.timeout = timeout;
    }

    public long getRetryInterval() {
      return retryInterval;
    }

    public void setRetryInterval(long retryInterval) {
      this.retryInterval = retryInterval;
    }

    public long getTtl() {
      return ttl;
    }

    public void setTtl(long ttl) {
      this.ttl = ttl;
    }

    public long getCallTimeout() {
      return callTimeout;
    }

    public void setCallTimeout(long callTimeout) {
      this.callTimeout = callTimeout;
    }
  }

  public static class Listener {
    private String concurrency = "1-5";
    private long receiveTimeout = 30000;
    private long recoveryInterval = 5000;
    private int maxMessagesPerTask = 1;
    private int idleConsumerLimit = 1;
    private int idleTaskExecutionLimit = 1;

    public String getConcurrency() {
      return concurrency;
    }

    public void setConcurrency(String concurrency) {
      this.concurrency = concurrency;
    }

    public long getReceiveTimeout() {
      return receiveTimeout;
    }

    public void setReceiveTimeout(long receiveTimeout) {
      this.receiveTimeout = receiveTimeout;
    }

    public long getRecoveryInterval() {
      return recoveryInterval;
    }

    public void setRecoveryInterval(long recoveryInterval) {
      this.recoveryInterval = recoveryInterval;
    }

    public int getMaxMessagesPerTask() {
      return maxMessagesPerTask;
    }

    public void setMaxMessagesPerTask(int maxMessagesPerTask) {
      this.maxMessagesPerTask = maxMessagesPerTask;
    }

    public int getIdleConsumerLimit() {
      return idleConsumerLimit;
    }

    public void setIdleConsumerLimit(int idleConsumerLimit) {
      this.idleConsumerLimit = idleConsumerLimit;
    }

    public int getIdleTaskExecutionLimit() {
      return idleTaskExecutionLimit;
    }

    public void setIdleTaskExecutionLimit(int idleTaskExecutionLimit) {
      this.idleTaskExecutionLimit = idleTaskExecutionLimit;
    }
  }
}
