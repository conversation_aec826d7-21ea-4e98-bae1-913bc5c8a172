package com.volvo.tisp.s1vas.monitoring;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jms.listener.DefaultMessageListenerContainer;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service to monitor JMS threads and detect potential blocking issues.
 * This helps identify threads stuck in TIMED_WAITING state.
 * Only active when JMS is enabled.
 */
@Service
@ConditionalOnProperty(name = "tisp.jms.enabled", havingValue = "true", matchIfMissing = false)
public class JmsMonitoringService {

    private static final Logger LOG = LoggerFactory.getLogger(JmsMonitoringService.class);
    
    private final ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
    private final MBeanServer mBeanServer = ManagementFactory.getPlatformMBeanServer();

    @Autowired(required = false)
    private DefaultMessageListenerContainer tispJmsListenerContainer;

    /**
     * Monitor JMS threads every 5 minutes to detect blocking issues
     */
    @Scheduled(fixedRate = 300000) // 5 minutes
    public void monitorJmsThreads() {
        try {
            // Get all thread information
            ThreadInfo[] threadInfos = threadMXBean.dumpAllThreads(false, false);
            
            // Filter JMS-related threads
            List<ThreadInfo> jmsThreads = Arrays.stream(threadInfos)
                .filter(threadInfo -> threadInfo.getThreadName().contains("JmsControllerEndpointContainer") ||
                                    threadInfo.getThreadName().contains("DefaultMessageListenerContainer") ||
                                    threadInfo.getThreadName().contains("artemis") ||
                                    threadInfo.getThreadName().contains("ActiveMQ"))
                .collect(Collectors.toList());

            if (!jmsThreads.isEmpty()) {
                LOG.debug("Found {} JMS-related threads", jmsThreads.size());
                
                for (ThreadInfo threadInfo : jmsThreads) {
                    Thread.State state = threadInfo.getThreadState();
                    String threadName = threadInfo.getThreadName();
                    
                    // Log thread state information
                    LOG.debug("JMS Thread: {} - State: {} - Blocked Time: {} ms", 
                        threadName, state, threadInfo.getBlockedTime());
                    
                    // Check for problematic states
                    if (state == Thread.State.TIMED_WAITING || state == Thread.State.WAITING) {
                        long blockedTime = threadInfo.getBlockedTime();
                        
                        // Log warning if thread has been blocked for more than 1 minute
                        if (blockedTime > 60000) {
                            LOG.warn("JMS Thread {} has been in {} state for {} ms. " +
                                "This may indicate a blocking issue.", 
                                threadName, state, blockedTime);
                            
                            // Log stack trace for debugging
                            StackTraceElement[] stackTrace = threadInfo.getStackTrace();
                            if (stackTrace.length > 0) {
                                LOG.warn("Stack trace for blocked thread {}:", threadName);
                                for (int i = 0; i < Math.min(10, stackTrace.length); i++) {
                                    LOG.warn("  at {}", stackTrace[i]);
                                }
                            }
                        }
                    }
                }
            }
            
            // Monitor JMS listener container if available
            if (tispJmsListenerContainer != null) {
                monitorListenerContainer();
            }
            
        } catch (Exception e) {
            LOG.error("Error monitoring JMS threads", e);
        }
    }

    /**
     * Monitor the TISP JMS listener container specifically
     */
    private void monitorListenerContainer() {
        try {
            boolean isRunning = tispJmsListenerContainer.isRunning();
            boolean isActive = tispJmsListenerContainer.isActive();
            int activeConsumerCount = tispJmsListenerContainer.getActiveConsumerCount();
            int scheduledConsumerCount = tispJmsListenerContainer.getScheduledConsumerCount();
            
            LOG.debug("TISP JMS Listener Container - Running: {}, Active: {}, " +
                "Active Consumers: {}, Scheduled Consumers: {}", 
                isRunning, isActive, activeConsumerCount, scheduledConsumerCount);
            
            // Check for potential issues
            if (isRunning && !isActive) {
                LOG.warn("TISP JMS Listener Container is running but not active. " +
                    "This may indicate a connection issue.");
            }
            
            if (activeConsumerCount == 0 && scheduledConsumerCount == 0) {
                LOG.warn("TISP JMS Listener Container has no active or scheduled consumers. " +
                    "This may indicate a configuration issue.");
            }
            
        } catch (Exception e) {
            LOG.error("Error monitoring TISP JMS listener container", e);
        }
    }

    /**
     * Get JMS connection pool statistics if available
     */
    @Scheduled(fixedRate = 600000) // 10 minutes
    public void logJmsStatistics() {
        try {
            // Try to get Artemis connection pool statistics
            ObjectName artemisConnections = new ObjectName("org.apache.activemq.artemis:broker=*,component=connections,*");
            
            if (mBeanServer.queryNames(artemisConnections, null).size() > 0) {
                LOG.debug("Artemis connection pool statistics available");
                // Additional statistics logging can be added here
            }
            
        } catch (Exception e) {
            LOG.debug("Unable to retrieve JMS statistics: {}", e.getMessage());
        }
    }

    /**
     * Force restart of JMS listener container if it appears to be stuck
     */
    public void restartJmsListenerContainer() {
        if (tispJmsListenerContainer != null) {
            try {
                LOG.warn("Attempting to restart TISP JMS Listener Container due to blocking issue");
                
                tispJmsListenerContainer.stop();
                Thread.sleep(5000); // Wait 5 seconds
                tispJmsListenerContainer.start();
                
                LOG.info("TISP JMS Listener Container restarted successfully");
                
            } catch (Exception e) {
                LOG.error("Failed to restart TISP JMS Listener Container", e);
            }
        }
    }
}
