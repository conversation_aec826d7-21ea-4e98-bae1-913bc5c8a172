package com.volvo.tisp.s1vas.monitoring;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for JMS Monitoring Service to ensure proper thread monitoring.
 */
@SpringBootTest
@ActiveProfiles("local_local_local")
public class JmsMonitoringServiceTest {

    @Autowired
    private JmsMonitoringService jmsMonitoringService;

    @Test
    public void testJmsMonitoringServiceExists() {
        assertNotNull(jmsMonitoringService, "JMS Monitoring Service should be configured");
    }

    @Test
    public void testMonitorJmsThreadsExecution() {
        // Test that monitoring method executes without throwing exceptions
        assertDoesNotThrow(() -> {
            jmsMonitoringService.monitorJmsThreads();
        }, "Monitor JMS threads should execute without exceptions");
    }

    @Test
    public void testLogJmsStatisticsExecution() {
        // Test that statistics logging executes without throwing exceptions
        assertDoesNotThrow(() -> {
            jmsMonitoringService.logJmsStatistics();
        }, "Log JMS statistics should execute without exceptions");
    }

    @Test
    public void testRestartJmsListenerContainerExecution() {
        // Test that restart method executes without throwing exceptions
        assertDoesNotThrow(() -> {
            jmsMonitoringService.restartJmsListenerContainer();
        }, "Restart JMS listener container should execute without exceptions");
    }

    @Test
    public void testMonitoringPerformance() {
        // Test that monitoring completes within reasonable time
        long startTime = System.currentTimeMillis();
        
        jmsMonitoringService.monitorJmsThreads();
        
        long duration = System.currentTimeMillis() - startTime;
        assertTrue(duration < 5000, 
            "JMS thread monitoring should complete within 5 seconds, took: " + duration + "ms");
    }
}
