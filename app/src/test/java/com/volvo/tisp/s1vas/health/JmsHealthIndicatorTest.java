package com.volvo.tisp.s1vas.health;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.Status;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for JMS Health Indicator to ensure proper monitoring of JMS connections.
 */
@SpringBootTest
@ActiveProfiles("local_local_local")
public class JmsHealthIndicatorTest {

    @Autowired
    private JmsHealthIndicator jmsHealthIndicator;

    @Test
    public void testJmsHealthIndicatorExists() {
        assertNotNull(jmsHealthIndicator, "JMS Health Indicator should be configured");
    }

    @Test
    public void testJmsHealthCheck() {
        // Perform health check
        Health health = jmsHealthIndicator.health();
        assertNotNull(health, "Health check should return a result");
        
        // The health status might be DOWN if broker is not available in test environment
        // but the important thing is that it doesn't hang or block
        Status status = health.getStatus();
        assertTrue(status.equals(Status.UP) || status.equals(Status.DOWN), 
            "Health status should be either UP or DOWN, not hanging");
        
        // Check that health details are provided
        assertNotNull(health.getDetails(), "Health details should be provided");
        
        if (status.equals(Status.UP)) {
            // If UP, verify expected details
            assertTrue(health.getDetails().containsKey("connectionFactory"), 
                "Should contain connection factory info");
            assertTrue(health.getDetails().containsKey("status"), 
                "Should contain status info");
        } else {
            // If DOWN, should contain error information
            assertTrue(health.getDetails().containsKey("error"), 
                "Should contain error information when DOWN");
        }
    }

    @Test
    public void testHealthCheckPerformance() {
        // Test that health check completes within reasonable time
        long startTime = System.currentTimeMillis();
        
        Health health = jmsHealthIndicator.health();
        
        long duration = System.currentTimeMillis() - startTime;
        assertTrue(duration < 10000, 
            "Health check should complete within 10 seconds, took: " + duration + "ms");
        
        assertNotNull(health, "Health check should return a result");
    }
}
