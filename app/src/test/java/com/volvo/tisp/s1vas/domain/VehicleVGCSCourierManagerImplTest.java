package com.volvo.tisp.s1vas.domain;

import com.volvo.tisp.courier.ChoreJmsClient;
import com.volvo.tisp.s1vas.util.Sems1VehicleTestConstants;
import com.wirelesscar.courier.data.courier._1_1.Chore;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class VehicleVGCSCourierManagerImplTest {

  @InjectMocks VehicleVGCSCourierManagerImpl vehicleVGCSCourierManagerImpl;
  @Mock ChoreJmsClient choreJmsClientImpl;

  // @Test
  void testHandleCourierNotificationSuccessful() {
    Mockito.when(choreJmsClientImpl.send(Mockito.any(Chore.class)))
        .thenReturn(new CompletableFuture<>());
    vehicleVGCSCourierManagerImpl.handleCourierNotification(
        Sems1VehicleTestConstants.PRIMARY_ID, Sems1VehicleTestConstants.REQUEST_THRESHOLD);
    Mockito.verify(choreJmsClientImpl, Mockito.times(1)).send(Mockito.any(Chore.class));
  }

  // @Test
  void testHandleCourierNotificationSuccessful1() {
    Mockito.when(choreJmsClientImpl.send(Mockito.any(Chore.class)))
        .thenReturn(new CompletableFuture<>());
    vehicleVGCSCourierManagerImpl.handleCourierNotification(
        Sems1VehicleTestConstants.PRIMARY_ID, Sems1VehicleTestConstants.DOWNLOAD_THRESHOLD);
    Mockito.verify(choreJmsClientImpl, Mockito.times(1)).send(Mockito.any(Chore.class));
  }

  // @Test
  void testHandleCourierNotificationSuccessful2() {
    Mockito.when(choreJmsClientImpl.send(Mockito.any(Chore.class)))
        .thenReturn(new CompletableFuture<>());
    vehicleVGCSCourierManagerImpl.handleCourierNotification(
        Sems1VehicleTestConstants.PRIMARY_ID, null);
    Mockito.verify(choreJmsClientImpl, Mockito.times(1)).send(Mockito.any(Chore.class));
  }
}
