package com.volvo.tisp.s1vas.conf;

import org.apache.activemq.artemis.jms.client.ActiveMQConnectionFactory;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jms.connection.CachingConnectionFactory;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.listener.DefaultMessageListenerContainer;
import org.springframework.test.context.ActiveProfiles;

import javax.jms.Connection;
import javax.jms.ConnectionFactory;
import javax.jms.JMSException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to validate JMS configuration and ensure thread blocking issues are resolved.
 */
@SpringBootTest
@ActiveProfiles("local_local_local")
public class JmsConfigTest {

    @Autowired
    private ConnectionFactory connectionFactory;

    @Autowired
    private JmsTemplate jmsTemplate;

    @Autowired
    private JmsProperties jmsProperties;

    @Autowired(required = false)
    private DefaultMessageListenerContainer tispJmsListenerContainer;

    @Test
    public void testJmsPropertiesLoaded() {
        assertNotNull(jmsProperties, "JMS properties should be loaded");
        assertTrue(jmsProperties.isEnabled(), "JMS should be enabled");
        
        // Test consumer properties
        assertEquals(30000, jmsProperties.getConsumer().getTimeout(), 
            "Consumer timeout should be 30 seconds");
        assertEquals(30000, jmsProperties.getConsumer().getMaxWait(), 
            "Consumer max wait should be 30 seconds");
        assertEquals(1024 * 1024, jmsProperties.getConsumer().getWindowSize(), 
            "Consumer window size should be 1MB");
        
        // Test connection properties
        assertEquals(10, jmsProperties.getConnection().getPoolSize(), 
            "Connection pool size should be 10");
        assertEquals(60000, jmsProperties.getConnection().getTimeout(), 
            "Connection timeout should be 60 seconds");
        assertEquals(5000, jmsProperties.getConnection().getRetryInterval(), 
            "Retry interval should be 5 seconds");
        
        // Test listener properties
        assertEquals("1-5", jmsProperties.getListener().getConcurrency(), 
            "Listener concurrency should be 1-5");
        assertEquals(30000, jmsProperties.getListener().getReceiveTimeout(), 
            "Listener receive timeout should be 30 seconds");
        assertEquals(5000, jmsProperties.getListener().getRecoveryInterval(), 
            "Recovery interval should be 5 seconds");
    }

    @Test
    public void testConnectionFactoryConfiguration() {
        assertNotNull(connectionFactory, "Connection factory should be configured");
        assertTrue(connectionFactory instanceof CachingConnectionFactory, 
            "Connection factory should be a CachingConnectionFactory");
        
        CachingConnectionFactory cachingFactory = (CachingConnectionFactory) connectionFactory;
        assertEquals(10, cachingFactory.getSessionCacheSize(), 
            "Session cache size should be 10");
        assertTrue(cachingFactory.isCacheConsumers(), 
            "Should cache consumers");
        assertTrue(cachingFactory.isCacheProducers(), 
            "Should cache producers");
        assertTrue(cachingFactory.isReconnectOnException(), 
            "Should reconnect on exception");
    }

    @Test
    public void testJmsTemplateConfiguration() {
        assertNotNull(jmsTemplate, "JMS template should be configured");
        assertEquals(30000, jmsTemplate.getReceiveTimeout(), 
            "JMS template receive timeout should be 30 seconds");
        assertFalse(jmsTemplate.isDeliveryPersistent(), 
            "Delivery should not be persistent");
        assertEquals(300000, jmsTemplate.getTimeToLive(), 
            "Time to live should be 5 minutes");
    }

    @Test
    public void testConnectionCreationAndTimeout() throws JMSException {
        // Test that we can create a connection without blocking
        long startTime = System.currentTimeMillis();
        
        Connection connection = connectionFactory.createConnection();
        assertNotNull(connection, "Should be able to create connection");
        
        long connectionTime = System.currentTimeMillis() - startTime;
        assertTrue(connectionTime < 5000, 
            "Connection creation should complete within 5 seconds, took: " + connectionTime + "ms");
        
        // Test connection start/stop without blocking
        startTime = System.currentTimeMillis();
        connection.start();
        connection.stop();
        connection.close();
        
        long operationTime = System.currentTimeMillis() - startTime;
        assertTrue(operationTime < 5000, 
            "Connection operations should complete within 5 seconds, took: " + operationTime + "ms");
    }

    @Test
    public void testTispJmsListenerContainerConfiguration() {
        if (tispJmsListenerContainer != null) {
            // Test that the container is properly configured
            assertEquals(1, tispJmsListenerContainer.getConcurrentConsumers(), 
                "Should have 1 concurrent consumer");
            assertEquals(5, tispJmsListenerContainer.getMaxConcurrentConsumers(), 
                "Should have max 5 concurrent consumers");
            assertEquals(30000, tispJmsListenerContainer.getReceiveTimeout(), 
                "Receive timeout should be 30 seconds");
            assertEquals(5000, tispJmsListenerContainer.getRecoveryInterval(), 
                "Recovery interval should be 5 seconds");
            assertEquals(1, tispJmsListenerContainer.getIdleConsumerLimit(), 
                "Idle consumer limit should be 1");
            assertEquals(1, tispJmsListenerContainer.getMaxMessagesPerTask(), 
                "Max messages per task should be 1");
            assertFalse(tispJmsListenerContainer.isSessionTransacted(), 
                "Session should not be transacted");
            assertTrue(tispJmsListenerContainer.isAutoStartup(), 
                "Should auto startup");
        }
    }

    @Test
    public void testArtemisConnectionFactorySettings() {
        // Get the underlying Artemis connection factory
        CachingConnectionFactory cachingFactory = (CachingConnectionFactory) connectionFactory;
        ConnectionFactory targetFactory = cachingFactory.getTargetConnectionFactory();
        
        assertTrue(targetFactory instanceof ActiveMQConnectionFactory, 
            "Target factory should be ActiveMQConnectionFactory");
        
        ActiveMQConnectionFactory artemisFactory = (ActiveMQConnectionFactory) targetFactory;
        
        // Test timeout settings that prevent thread blocking
        assertEquals(1024 * 1024, artemisFactory.getConsumerWindowSize(), 
            "Consumer window size should be 1MB");
        assertEquals(60000, artemisFactory.getConnectionTTL(), 
            "Connection TTL should be 60 seconds");
        assertEquals(30000, artemisFactory.getCallTimeout(), 
            "Call timeout should be 30 seconds");
        assertEquals(30000, artemisFactory.getCallFailoverTimeout(), 
            "Call failover timeout should be 30 seconds");
        assertTrue(artemisFactory.isCacheLargeMessagesClient(), 
            "Should cache large messages");
    }

    @Test
    public void testJmsTemplateReceiveTimeout() {
        // Test that JMS template operations don't block indefinitely
        long startTime = System.currentTimeMillis();
        
        try {
            // This should timeout after 30 seconds, not block indefinitely
            Object result = jmsTemplate.receiveAndConvert("test.queue.nonexistent");
            assertNull(result, "Should return null for non-existent queue");
        } catch (Exception e) {
            // Expected - queue doesn't exist or broker not available
            assertTrue(e.getMessage().contains("timeout") || 
                      e.getMessage().contains("connection") ||
                      e.getMessage().contains("broker"),
                "Exception should be related to timeout or connection: " + e.getMessage());
        }
        
        long operationTime = System.currentTimeMillis() - startTime;
        assertTrue(operationTime < 35000, 
            "Receive operation should timeout within 35 seconds, took: " + operationTime + "ms");
    }
}
