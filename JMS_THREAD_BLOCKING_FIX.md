# JMS Thread Blocking Issue Fix

## Problem Description

After the TISP framework update to version 157, JMS threads were getting stuck in TIMED_WAITING state, specifically:
- Thread: "com.volvo.tisp.framework.jms.JmsControllerEndpointContainer#0-5717"
- State: TIMED_WAITING (on object monitor)
- Blocking at: `ClientConsumerImpl.receive()` in Apache ActiveMQ Artemis

## Root Cause Analysis

The issue was caused by:
1. **Missing JMS consumer timeout configuration** - Default receive timeout was too long or infinite
2. **Lack of connection pool configuration** - No proper connection management for JMS consumers
3. **Missing message listener container settings** - No concurrency or timeout controls
4. **TISP framework update changes** - New version changed default timeout behaviors

## Solution Implementation

### 1. JMS Configuration Class (`JmsConfig.java`)

Created a comprehensive JMS configuration class that addresses all timeout and connection pool issues:

- **Connection Factory Configuration**: Proper timeout settings for Artemis connections
- **Connection Pooling**: Caching connection factory with session management
- **Consumer Timeout Settings**: Explicit receive timeouts to prevent indefinite blocking
- **Message Listener Container**: Proper concurrency and timeout controls

### 2. Application Configuration Updates (`application.yml`)

Added comprehensive JMS configuration properties:

```yaml
spring:
  artemis:
    broker-url: tcp://mockhost:61616
    consumer:
      receive-timeout: 30000  # 30 seconds timeout for receive operations
    pool:
      enabled: true
      max-connections: 10
      idle-timeout: 300000  # 5 minutes
      max-sessions-per-connection: 100
    listener:
      acknowledge-mode: auto
      concurrency: 1-5  # Min-Max concurrent consumers
      receive-timeout: 30000  # 30 seconds timeout
      recovery-interval: 5000  # 5 seconds recovery interval

tisp:
  jms:
    enabled: true
    consumer:
      timeout: 30000
      max-wait: 30000
    connection:
      pool-size: 10
      timeout: 60000
      retry-interval: 5000
```

### 3. JMS Properties Class (`JmsProperties.java`)

Created a configuration properties class to manage all JMS settings with proper defaults and environment-specific overrides.

### 4. Health Monitoring (`JmsHealthIndicator.java`)

Added JMS health indicator to monitor connection status and detect issues early.

### 5. Thread Monitoring (`JmsMonitoringService.java`)

Implemented monitoring service that:
- Tracks JMS thread states every 5 minutes
- Detects threads stuck in TIMED_WAITING state
- Logs warnings for threads blocked longer than 1 minute
- Provides automatic restart capability for stuck containers

## Key Configuration Parameters

### Critical Timeout Settings

1. **Consumer Receive Timeout**: `30000ms` (30 seconds)
   - Prevents indefinite blocking in `ClientConsumerImpl.receive()`
   - Can be adjusted based on message frequency

2. **Connection TTL**: `60000ms` (60 seconds)
   - Prevents hanging connections
   - Ensures connections are refreshed regularly

3. **Call Timeout**: `30000ms` (30 seconds)
   - Timeout for JMS operations
   - Prevents blocking on slow broker responses

4. **Recovery Interval**: `5000ms` (5 seconds)
   - How quickly to retry failed connections
   - Balances responsiveness with resource usage

### Connection Pool Settings

1. **Max Connections**: `10`
   - Maximum number of cached connections
   - Adjust based on load requirements

2. **Session Cache Size**: `10`
   - Number of cached sessions per connection
   - Improves performance by reusing sessions

3. **Consumer Window Size**: `1MB`
   - Amount of data to prefetch
   - Prevents memory issues with large messages

## Environment-Specific Configuration

### Production/QA Environments

For production and QA environments, consider these adjustments:

```yaml
tisp:
  jms:
    consumer:
      timeout: 60000  # Longer timeout for production
      max-wait: 60000
    connection:
      pool-size: 20  # More connections for higher load
      timeout: 120000  # Longer connection timeout
    listener:
      concurrency: "2-10"  # Higher concurrency for production
      receive-timeout: 60000
```

### Local Development

For local development, use shorter timeouts for faster feedback:

```yaml
tisp:
  jms:
    consumer:
      timeout: 15000  # Shorter timeout for development
      max-wait: 15000
    connection:
      pool-size: 5  # Fewer connections for local
      timeout: 30000
```

## Monitoring and Troubleshooting

### Health Check Endpoint

The JMS health indicator is available at: `/actuator/health/jms`

### Log Monitoring

Monitor these log messages:
- `JMS Thread {} has been in {} state for {} ms` - Indicates potential blocking
- `TISP JMS Listener Container restarted successfully` - Automatic recovery
- `JMS Health Check failed` - Connection issues

### Manual Recovery

If threads are still getting stuck, you can manually restart the JMS container:

```java
@Autowired
private JmsMonitoringService jmsMonitoringService;

// Force restart if needed
jmsMonitoringService.restartJmsListenerContainer();
```

## Testing the Fix

1. **Deploy the updated configuration**
2. **Monitor thread states** using the monitoring service
3. **Check health endpoint** for JMS connection status
4. **Verify no TIMED_WAITING threads** in JmsControllerEndpointContainer
5. **Test message processing** to ensure functionality is maintained

## Rollback Plan

If issues occur, you can quickly rollback by:
1. Removing the `@Import(JmsConfig.class)` from `AppConfig.java`
2. Reverting the `application.yml` changes
3. Redeploying the application

The application will fall back to default TISP framework JMS configuration.
